"""
Azure Blob Storage sink for Loguru logging.

This module provides a custom Loguru sink that uploads log entries to Azure Blob Storage
with proper error handling, buffering, and timestamped log file naming conventions.
"""

import sys
import threading
from datetime import datetime
from typing import Optional

from azure.storage.blob import BlobServiceClient
from loguru import logger


class AzureBlobStorageSink:
    """
    Custom Loguru sink for Azure Blob Storage logging.

    This sink uploads log entries to Azure Blob Storage with proper error handling,
    buffering, and timestamped log file naming conventions. Log entries are uploaded
    immediately when the buffer reaches the maximum size.
    """

    def __init__(self, connection_string: str, container_name: str, blob_prefix: str, environment: str = 'UNKNOWN'):
        """
        Initialize Azure Blob Storage sink.

        Args:
            connection_string: Azure Storage connection string
            container_name: Name of the blob container
            blob_prefix: Prefix for blob names (e.g., 'score')
            environment: Deployment environment name (e.g., 'PROD', 'UAT', 'TEST')
        """
        self.connection_string = connection_string
        self.container_name = container_name
        self.blob_prefix = blob_prefix
        self.environment = environment
        self.blob_service_client: Optional[BlobServiceClient] = None
        self.current_blob_name: Optional[str] = None
        self.log_buffer = []
        self.buffer_lock = threading.Lock()
        self.max_buffer_size = 50  # Buffer size for memory efficiency

        # Initialize blob service client
        self._initialize_blob_client()

    def _initialize_blob_client(self) -> None:
        """Initialize Azure Blob Service Client with error handling."""
        try:
            self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)

            # Create container if it doesn't exist
            try:
                self.blob_service_client.create_container(self.container_name)
                logger.info(f'📦 Created Azure Blob container: {self.container_name}')
            except Exception:
                # Container might already exist, which is fine
                pass

            # Generate environment-specific timestamped blob name
            timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
            self.current_blob_name = f'{self.blob_prefix}_{self.environment}_{timestamp}.log'

            logger.info(
                f'☁️ Azure Blob Storage logging initialized - Container: {self.container_name}, Environment: {self.environment}, Blob: {self.current_blob_name}'
            )

        except Exception as e:
            logger.error(f'❌ Failed to initialize Azure Blob Storage client: {str(e)}')
            raise

    def _upload_buffer(self) -> None:
        """Upload current log buffer to Azure Blob Storage."""
        if not self.log_buffer or not self.blob_service_client or not self.current_blob_name:
            return

        try:
            # Create a copy of buffer and clear original to free memory quickly
            buffer_copy = self.log_buffer.copy()
            buffer_count = len(self.log_buffer)
            self.log_buffer.clear()

            # Prepare log content efficiently
            log_content = '\n'.join(buffer_copy) + '\n'

            # Clear the copy to free memory
            del buffer_copy

            # Get blob client
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name, blob=self.current_blob_name
            )

            # Check if blob exists and append, otherwise create new
            try:
                # Try to download existing content
                existing_content = blob_client.download_blob().readall().decode('utf-8')
                log_content = existing_content + log_content
            except Exception:
                # Blob doesn't exist yet, which is fine
                pass

            # Upload the combined content
            blob_client.upload_blob(log_content, overwrite=True)

            logger.debug(f'✅ Successfully uploaded {buffer_count} log entries to Azure Blob Storage')

        except Exception as e:
            logger.error(f'❌ Failed to upload logs to Azure Blob Storage: {str(e)}')

    def write(self, message) -> None:
        """
        Write log message to buffer for Azure Blob Storage upload.

        Args:
            message: Log message from Loguru
        """
        try:
            # Format message for blob storage (without ANSI color codes)
            formatted_message = (
                '{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}'.format(
                    **message.record
                )
            )

            with self.buffer_lock:
                self.log_buffer.append(formatted_message)

                # Force upload if buffer is full
                if len(self.log_buffer) >= self.max_buffer_size:
                    self._upload_buffer()

        except Exception as e:
            # Fallback to stderr if Azure upload fails
            print(f'❌ Azure Blob Storage sink error: {str(e)}', file=sys.stderr)
