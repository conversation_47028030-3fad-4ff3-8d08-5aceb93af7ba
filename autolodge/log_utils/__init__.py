"""
Autolodge Log Utils Package

This package provides modular logging components for the autolodge application,
including Azure Blob Storage integration and local file logging with fallback mechanisms.

Components:
- azure_sink: Azure Blob Storage logging sink
- config: Environment variable configuration handling
- utils: Shared logging utilities and setup functions
"""

from .azure_sink import AzureBlobStorageSink
from .config import AzureLoggingConfig, check_azure_dependencies
from .utils import setup_logging

__all__ = ['AzureBlobStorageSink', 'AzureLoggingConfig', 'setup_logging', 'check_azure_dependencies']
